#!/bin/bash

# 测试版本的多线程并行执行脚本

SCRIPT_DIR="./test_scripts"
MAX_PARALLEL_JOBS=${MAX_JOBS:-2}
RESULTS_DIR="./temp_results_$(date +%s)"
LOG_DIR="./temp_logs_$(date +%s)"

echo "=== 测试多线程爬虫执行器 ==="
echo "脚本目录: $SCRIPT_DIR"
echo "最大并行任务数: $MAX_PARALLEL_JOBS"
echo "结果目录: $RESULTS_DIR"
echo "日志目录: $LOG_DIR"

# 创建临时目录
mkdir -p "$RESULTS_DIR"
mkdir -p "$LOG_DIR"

# 清理函数
cleanup() {
    echo "清理临时目录..."
    rm -rf "$RESULTS_DIR"
    rm -rf "$LOG_DIR"
}
trap cleanup EXIT

# 执行单个文件
execute_single_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    local log_file="$LOG_DIR/${file_name}.log"
    local result_file="$RESULTS_DIR/${file_name}.result"
    local start_time=$(date +%s)
    
    echo "[$(date '+%H:%M:%S')] 开始执行: $file_name"
    
    {
        echo "=== 开始时间: $(date) ==="
        python "$file_path" 2>&1
        local exit_code=$?
        echo "=== 结束时间: $(date) ==="
        echo "=== 退出码: $exit_code ==="
        echo "$exit_code" > "${result_file}.exit_code"
    } > "$log_file" 2>&1
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 分析结果
    analyze_result "$file_name" "$log_file" "$result_file" "$duration"
    echo "[$(date '+%H:%M:%S')] 完成执行: $file_name (耗时: ${duration}s)"
}

# 分析结果
analyze_result() {
    local file_name="$1"
    local log_file="$2"
    local result_file="$3"
    local duration="$4"
    local exit_code_file="${result_file}.exit_code"
    
    local exit_code=1
    if [ -f "$exit_code_file" ]; then
        exit_code=$(cat "$exit_code_file")
    fi
    
    local success_count=0
    local brand_name=""
    
    if [ -f "$log_file" ] && grep -q "===new_record===" "$log_file"; then
        success_line=$(grep "===new_record===" "$log_file" | tail -1)
        brand_name=$(echo "$success_line" | sed 's/.*===new_record===\([^,]*\).*/\1/' | tr -d ' ')
        success_count=$(echo "$success_line" | grep -o '[0-9]\+' | tail -1)
        if [ -z "$success_count" ]; then
            success_count=0
        fi
    fi
    
    # 生成结果JSON
    cat > "$result_file" << EOF
{
    "file_name": "$file_name",
    "brand_name": "$brand_name",
    "exit_code": $exit_code,
    "success_count": $success_count,
    "duration": $duration,
    "timestamp": "$(date '+%Y-%m-%d %H:%M:%S')"
}
EOF
}

# 控制并发
control_concurrency() {
    while [ $(jobs -r | wc -l) -ge $MAX_PARALLEL_JOBS ]; do
        sleep 1
    done
}

# 等待所有任务完成
wait_for_jobs() {
    while [ $(jobs -r | wc -l) -gt 0 ]; do
        echo "等待 $(jobs -r | wc -l) 个任务完成..."
        sleep 2
    done
}

# 主执行逻辑
main() {
    local start_time=$(date +%s)
    local python_files=$(find "$SCRIPT_DIR" -name "*.py" | sort)
    
    if [ -z "$python_files" ]; then
        echo "错误: 没有找到Python文件"
        exit 1
    fi
    
    echo "准备执行以下文件:"
    echo "$python_files"
    echo ""
    
    # 并行执行
    for file in $python_files; do
        control_concurrency
        execute_single_file "$file" &
        sleep 0.5
    done
    
    echo "所有任务已启动，等待完成..."
    wait_for_jobs
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    # 汇总结果
    echo ""
    echo "=== 执行结果 ==="
    local total_success=0
    local total_failed=0
    
    for result_file in "$RESULTS_DIR"/*.result; do
        if [ ! -f "$result_file" ]; then
            continue
        fi
        
        local file_name=$(grep '"file_name"' "$result_file" | cut -d'"' -f4)
        local brand_name=$(grep '"brand_name"' "$result_file" | cut -d'"' -f4)
        local exit_code=$(grep '"exit_code"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local success_count=$(grep '"success_count"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local duration=$(grep '"duration"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        
        if [ "$exit_code" = "0" ] && [ "$success_count" -gt "0" ]; then
            echo "✓ $file_name ($brand_name): 成功 ($success_count 条记录, ${duration}s)"
            total_success=$((total_success + 1))
        else
            echo "✗ $file_name: 失败 (退出码: $exit_code, ${duration}s)"
            total_failed=$((total_failed + 1))
        fi
    done
    
    echo ""
    echo "=== 统计 ==="
    echo "成功: $total_success"
    echo "失败: $total_failed"
    echo "总耗时: ${total_duration}s"
}

main "$@"
