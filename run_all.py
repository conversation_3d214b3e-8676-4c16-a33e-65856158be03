import os
import requests
import subprocess
from datetime import datetime
import logging
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import json
import tempfile
import shutil
import re

# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

status = {}
failed_status = {}
# 线程锁，用于保护共享变量
status_lock = threading.Lock()

FEISHU_NOTIFY_TOKEN = os.environ.get("FEISHU_NOTIFY_TOKEN", "")
formarter = "%H:%M:%S"
started_at = datetime.now().timestamp()

FILE_TO_EXECUTE = os.environ.get("FILE_TO_EXECUTE", "")

# 创建临时目录用于存储每个文件的执行结果
temp_dir = tempfile.mkdtemp(prefix="spider_results_")
logging.info(f"临时结果目录: {temp_dir}")

# Get the list of files and sort it
files = os.listdir("./scripts")
files.sort()
for file in files:
    if FILE_TO_EXECUTE != "":
        if file != FILE_TO_EXECUTE:
            logging.info(f"run {FILE_TO_EXECUTE} only, file skipped:{file}")
            continue
    if file.endswith(".py"):
        if "proxy_setup.py" in file:
            logging.info(f"skip this common file:{file}")
            continue
        try:
            logging.info(f"开始运行文件:{file}")
            result = subprocess.run(
                ["python3", f"./scripts/{file}"], capture_output=True
            )
            new_record_count = []
            stdout_content = ""
            last_line_of_stdout = ""
            if result.stdout is not None and len(result.stdout) > 1:
                stdout_content = result.stdout.decode(errors="ignore")
            elif result.stderr is not None:
                stdout_content = result.stderr.decode(errors="ignore")
            new_record_count = stdout_content.split("\n")

            for msg in new_record_count:
                print(f"{file}, {msg}")
            last_line_of_stdout = new_record_count[-2]
            new_record_count = last_line_of_stdout.split("===new_record===")
            logging.info(f"{file}, 最后一行日志:{new_record_count}")

            count = 0
            if len(new_record_count) > 1:
                count = new_record_count[1]
                status[file] = f"{count}, {datetime.now().strftime(formarter)}"
                logging.info(f"{file} 成功了!, 爬取商品数:{new_record_count}")
            else:
                failed_status[file] = (
                    f"爬取失败!{last_line_of_stdout}, {datetime.now().strftime(formarter)}"
                )
        except Exception as e:
            logging.error(f"文件运行失败:{file}, 异常信息:{e}")
            status[file] = f"Failed. Message: {e}"[0:100]

finished_at = datetime.now().timestamp()
time_cost_in_seconds = int(finished_at) - int(started_at)
# https://open.feishu.cn/open-apis/bot/v2/hook/4c945cb4-1ab8-450d-bb78-89db0f578cba
logging.info(
    f"status:{status},  failed_status:{failed_status}, time cost:{time_cost_in_seconds}s"
)

text_content = []
for key, value in status.items():
    text_content.append(f"- {value}\n")

if len(failed_status.items()) > 0:
    text_content.append("\n\n**以下爬虫失败:**\n")
    for key, value in failed_status.items():
        text_content.append(f"- {key}, {value}\n")


def send_feishu_notice_with_title_and_content(feishu_url, title, markdown_content):
    headers = {"Content-Type": "application/json"}
    data = {
        "msg_type": "interactive",
        "card": {
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**{title}**",
                    "tag": "lark_md",
                },
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_content,
                }
            ],
        },
    }
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result


markdown_content = f"{''.join(text_content)}\n\n**任务耗时:** {int(time_cost_in_seconds/3600)}时{int((time_cost_in_seconds%3600)/60)}分{time_cost_in_seconds%60}秒"

logging.info(f"markdown_content:{markdown_content}")

if len(FEISHU_NOTIFY_TOKEN) > 5:
    # 通知飞书
    headers = {"Content-Type": "application/json"}

    # Post the data
    url = f"https://open.feishu.cn/open-apis/bot/v2/hook/{FEISHU_NOTIFY_TOKEN}"
    feishu_result = send_feishu_notice_with_title_and_content(
        feishu_url=url,
        title=f"成功爬取了{len(status.items())}家竞对数据",
        markdown_content=markdown_content,
    )
    logging.info(f"通知飞书结果:{feishu_result}")
else:
    logging.info(f"无需飞书通知:{FEISHU_NOTIFY_TOKEN}")
