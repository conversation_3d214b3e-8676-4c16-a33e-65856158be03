#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本，用于验证多线程爬虫执行器的功能
模拟真实爬虫脚本的行为
"""

import time
import random
import sys
import os
from datetime import datetime

def simulate_spider_work():
    """模拟爬虫工作"""
    script_name = os.path.basename(__file__)
    brand_name = script_name.replace('_spider.py', '').replace('.py', '')
    
    print(f"[{datetime.now()}] 开始执行爬虫: {brand_name}")
    
    # 模拟随机执行时间 (5-15秒)
    work_time = random.randint(5, 15)
    print(f"模拟工作时间: {work_time}秒")
    
    for i in range(work_time):
        time.sleep(1)
        if i % 3 == 0:
            print(f"正在处理第 {i+1} 批数据...")
    
    # 模拟随机成功/失败
    success_rate = 0.8  # 80% 成功率
    if random.random() < success_rate:
        # 成功情况
        record_count = random.randint(10, 100)
        print(f"[{datetime.now()}] 爬取完成")
        print(f"===new_record==={brand_name}, 商品数:{record_count}")
        return 0
    else:
        # 失败情况
        print(f"[{datetime.now()}] 爬取失败: 网络连接超时")
        return 1

if __name__ == "__main__":
    exit_code = simulate_spider_work()
    sys.exit(exit_code)
