#!/bin/bash

# Docker环境专用的多线程爬虫执行器
# 解决了Docker容器中shell兼容性问题

set -e  # 遇到错误立即退出

# 配置参数
SCRIPT_DIR="./scripts"
MAX_PARALLEL_JOBS=${MAX_JOBS:-2}
RESULTS_DIR="./temp_results_$(date +%s)"
LOG_DIR="./temp_logs_$(date +%s)"
PID_DIR="./temp_pids_$(date +%s)"

# 环境变量设置
export APP_LOG_DIR=$(pwd)
export PYTHONPATH=$PYTHONPATH:$(pwd)

echo "=== Docker多线程爬虫执行器启动 ==="
echo "最大并行任务数: $MAX_PARALLEL_JOBS"
echo "结果目录: $RESULTS_DIR"
echo "日志目录: $LOG_DIR"
echo "PID目录: $PID_DIR"

# 创建临时目录
mkdir -p "$RESULTS_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

# 清理函数
cleanup() {
    echo "清理临时目录和进程..."
    # 杀死所有子进程
    if [ -d "$PID_DIR" ]; then
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                pid=$(cat "$pid_file" 2>/dev/null || echo "")
                if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
                    echo "终止进程: $pid"
                    kill "$pid" 2>/dev/null || true
                fi
            fi
        done
    fi
    rm -rf "$RESULTS_DIR" "$LOG_DIR" "$PID_DIR"
}

# 设置退出时清理
trap cleanup EXIT INT TERM

# 获取要执行的文件列表
get_python_files() {
    if [ -z "$FILE_TO_EXECUTE" ]; then
        find "$SCRIPT_DIR" -name "*.py" | grep -v proxy_setup | sort
    else
        echo "$SCRIPT_DIR/$FILE_TO_EXECUTE"
    fi
}

# 执行单个Python文件的函数
execute_single_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    local log_file="$LOG_DIR/${file_name}.log"
    local result_file="$RESULTS_DIR/${file_name}.result"
    local pid_file="$PID_DIR/${file_name}.pid"
    local start_time=$(date +%s)
    
    # 记录当前进程PID
    echo $$ > "$pid_file"
    
    echo "[$(date '+%H:%M:%S')] 开始执行: $file_name (PID: $$)"
    
    # 设置该文件专用的日志文件
    export APP_LOG_DIR="$LOG_DIR"
    export SPIDER_LOG_FILE="$log_file"
    
    # 执行Python脚本，捕获输出和返回码
    local exit_code=0
    {
        echo "=== 开始时间: $(date) ==="
        echo "=== 文件: $file_name ==="
        
        # 执行脚本并捕获所有输出
        python "$file_path" 2>&1
        exit_code=$?
        
        echo "=== 结束时间: $(date) ==="
        echo "=== 退出码: $exit_code ==="
        
    } > "$log_file" 2>&1
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 分析执行结果
    analyze_result "$file_name" "$log_file" "$result_file" "$duration" "$exit_code"
    
    echo "[$(date '+%H:%M:%S')] 完成执行: $file_name (耗时: ${duration}s)"
    
    # 清理PID文件
    rm -f "$pid_file"
}

# 分析单个文件的执行结果
analyze_result() {
    local file_name="$1"
    local log_file="$2"
    local result_file="$3"
    local duration="$4"
    local exit_code="$5"
    
    local success_count=0
    local brand_name=""
    local last_line=""
    
    if [ -f "$log_file" ]; then
        # 查找成功标记
        if grep -q "===new_record===" "$log_file"; then
            success_line=$(grep "===new_record===" "$log_file" | tail -1)
            brand_name=$(echo "$success_line" | sed 's/.*===new_record===\([^,]*\).*/\1/' | tr -d ' ')
            success_count=$(echo "$success_line" | grep -o '[0-9]\+' | tail -1)
            if [ -z "$success_count" ]; then
                success_count=0
            fi
        fi
        
        # 获取最后几行作为错误信息
        last_line=$(tail -5 "$log_file" | grep -v "^$" | tail -3 | tr '\n' ' ' | sed 's/[[:space:]]\+/ /g')
    fi
    
    # 生成结果JSON
    cat > "$result_file" << EOF
{
    "file_name": "$file_name",
    "brand_name": "$brand_name",
    "exit_code": $exit_code,
    "success_count": $success_count,
    "duration": $duration,
    "timestamp": "$(date '+%Y-%m-%d %H:%M:%S')",
    "last_output": "$last_line",
    "log_file": "$log_file"
}
EOF
    
    echo "[分析] $file_name: 退出码=$exit_code, 成功数=$success_count, 耗时=${duration}s"
}

# 等待所有任务完成（使用PID文件方式）
wait_for_all_tasks() {
    while true; do
        local running_count=0
        local running_tasks=""

        if [ -d "$PID_DIR" ]; then
            for pid_file in "$PID_DIR"/*.pid; do
                if [ -f "$pid_file" ]; then
                    pid=$(cat "$pid_file" 2>/dev/null || echo "")
                    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
                        # 提取任务名（从PID文件名中获取，去掉.py后缀使其更简洁）
                        task_name=$(basename "$pid_file" .pid | sed 's/_spider\.py$//' | sed 's/\.py$//')
                        running_count=$((running_count + 1))
                        if [ -z "$running_tasks" ]; then
                            running_tasks="$task_name"
                        else
                            running_tasks="$running_tasks, $task_name"
                        fi
                    else
                        # 进程已结束，删除PID文件
                        rm -f "$pid_file"
                    fi
                fi
            done
        fi

        if [ $running_count -eq 0 ]; then
            break
        fi

        echo "等待 $running_count 个任务完成: [$running_tasks]"
        sleep 5  # 改为5秒钟打印一次
    done
}

# 控制并发数量（使用PID文件方式）
control_concurrency() {
    while true; do
        local current_jobs=0
        if [ -d "$PID_DIR" ]; then
            for pid_file in "$PID_DIR"/*.pid; do
                if [ -f "$pid_file" ]; then
                    pid=$(cat "$pid_file" 2>/dev/null || echo "")
                    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
                        current_jobs=$((current_jobs + 1))
                    else
                        # 进程已结束，删除PID文件
                        rm -f "$pid_file"
                    fi
                fi
            done
        fi
        
        if [ $current_jobs -lt $MAX_PARALLEL_JOBS ]; then
            break
        fi
        sleep 1
    done
}

# 主执行逻辑
main() {
    local start_time=$(date +%s)
    local python_files=$(get_python_files)
    local total_files=$(echo "$python_files" | wc -l)
    
    if [ -z "$python_files" ]; then
        echo "错误: 没有找到要执行的Python文件"
        exit 1
    fi
    
    echo "准备执行 $total_files 个文件..."
    
    # 并行执行所有文件
    for file in $python_files; do
        if [ ! -f "$file" ]; then
            echo "警告: 文件不存在: $file"
            continue
        fi
        
        # 控制并发数量
        control_concurrency
        
        # 后台执行
        (
            # 在子shell中设置进程标题，便于识别
            exec -a "spider_$(basename "$file")" bash -c "execute_single_file '$file'"
        ) &

        echo "已启动: $(basename "$file")"
        sleep 1  # 给系统一点时间创建PID文件
    done
    
    echo "所有任务已启动，等待完成..."
    wait_for_all_tasks
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    # 汇总结果
    summarize_results "$total_duration"
}

# 汇总所有执行结果
summarize_results() {
    local total_duration="$1"
    local success_results=""
    local failed_results=""
    local total_success=0
    local total_failed=0
    
    echo "=== 汇总执行结果 ==="
    
    for result_file in "$RESULTS_DIR"/*.result; do
        if [ ! -f "$result_file" ]; then
            continue
        fi
        
        # 读取结果JSON (简单解析)
        local file_name=$(grep '"file_name"' "$result_file" | cut -d'"' -f4)
        local brand_name=$(grep '"brand_name"' "$result_file" | cut -d'"' -f4)
        local exit_code=$(grep '"exit_code"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local success_count=$(grep '"success_count"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local duration=$(grep '"duration"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local timestamp=$(grep '"timestamp"' "$result_file" | cut -d'"' -f4)
        
        # 判断成功条件：退出码为0且成功数量大于0
        if [ "$exit_code" = "0" ] && [ "$success_count" -gt "0" ]; then
            # 格式化成功结果，兼容原有的飞书通知格式
            local display_name="$brand_name"
            if [ -z "$brand_name" ]; then
                display_name="$file_name"
            fi
            success_results="${success_results}${display_name}:::${success_count}, ${timestamp}__EOF__"
            total_success=$((total_success + 1))
            echo "✓ $file_name ($display_name): 成功 ($success_count 条记录, ${duration}s)"
        else
            # 格式化失败结果
            local error_msg="执行失败"
            if [ "$exit_code" != "0" ]; then
                error_msg="退出码: $exit_code"
            elif [ "$success_count" = "0" ]; then
                error_msg="未爬取到数据"
            fi
            failed_results="${failed_results}${file_name}:::${error_msg}, ${timestamp}__EOF__"
            total_failed=$((total_failed + 1))
            echo "✗ $file_name: 失败 ($error_msg, ${duration}s)"
        fi
    done
    
    echo ""
    echo "=== 执行统计 ==="
    echo "总文件数: $((total_success + total_failed))"
    echo "成功: $total_success"
    echo "失败: $total_failed"
    echo "总耗时: ${total_duration}s ($(($total_duration/60))分$((total_duration%60))秒)"
    echo ""
    
    # 调用飞书通知
    if [ -f "./send_feishu_notification.py" ]; then
        echo "发送飞书通知..."
        python ./send_feishu_notification.py \
            --all_failed_result="$failed_results" \
            --all_success_result="$success_results" \
            --time_cost_in_seconds=$total_duration
    else
        echo "未找到飞书通知脚本，跳过通知"
    fi
}

# 执行主函数
main "$@"
